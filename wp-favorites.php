<?php
/**
 * Plugin Name: WP Favorites - WooCommerce Wishlist
 * Plugin URI: https://pixelhunter.pt
 * Description: A complete favorites/wishlist system for WooCommerce with Breakdance integration and full translation support.
 * Version: 1.0.0
 * Author: Pixe<PERSON><PERSON>unter
 * Author URI: https://pixelhunter.pt
 * Text Domain: wp-favorites
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * WC requires at least: 4.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WP_FAVORITES_VERSION', '1.0.0');
define('WP_FAVORITES_PLUGIN_FILE', __FILE__);
define('WP_FAVORITES_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_FAVORITES_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_FAVORITES_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('WP_FAVORITES_TEXT_DOMAIN', 'wp-favorites');

/**
 * Main WP Favorites Plugin Class
 */
class WP_Favorites_Plugin {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Plugin version
     */
    public $version = WP_FAVORITES_VERSION;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Plugin activation/deactivation
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Initialize plugin
        add_action('plugins_loaded', array($this, 'init'));
        
        // Load text domain
        add_action('init', array($this, 'load_textdomain'));
        
        // Check dependencies
        add_action('admin_init', array($this, 'check_dependencies'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(
                __('WP Favorites requires WooCommerce to be installed and active.', 'wp-favorites'),
                __('Plugin Activation Error', 'wp-favorites'),
                array('back_link' => true)
            );
        }
        
        // Create database tables if needed
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }
        
        // Load plugin classes
        $this->load_classes();
        
        // Initialize components
        $this->init_components();
    }
    
    /**
     * Load text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'wp-favorites',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }
    
    /**
     * Check plugin dependencies
     */
    public function check_dependencies() {
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
        }
    }
    
    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p>
                <?php 
                echo sprintf(
                    __('WP Favorites requires %s to be installed and active.', 'wp-favorites'),
                    '<strong>WooCommerce</strong>'
                );
                ?>
            </p>
        </div>
        <?php
    }
    
    /**
     * Load plugin classes
     */
    private function load_classes() {
        // Core classes
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-core.php';
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-admin.php';
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-frontend.php';
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-ajax.php';
        
        // Breakdance integration (if Breakdance is active)
        if (class_exists('Breakdance\PluginsAPI\PluginsController')) {
            require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-breakdance.php';
        }
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize core
        WP_Favorites_Core::get_instance();
        
        // Initialize admin (only in admin)
        if (is_admin()) {
            WP_Favorites_Admin::get_instance();
        }
        
        // Initialize frontend (only on frontend)
        if (!is_admin()) {
            WP_Favorites_Frontend::get_instance();
        }
        
        // Initialize AJAX (both admin and frontend)
        WP_Favorites_Ajax::get_instance();
        
        // Initialize Breakdance integration (if available)
        if (class_exists('Breakdance\PluginsAPI\PluginsController')) {
            WP_Favorites_Breakdance::get_instance();
        }
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            product_id bigint(20) NOT NULL,
            date_added datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY user_product (user_id, product_id),
            KEY user_id (user_id),
            KEY product_id (product_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_options = array(
            'favorites_page_id' => 0,
            'icon_position' => 'top-right',
            'icon_size' => 'medium',
            'icon_color' => '#ff0000',
            'icon_color_active' => '#ff6666',
            'grid_columns_desktop' => 4,
            'grid_columns_tablet' => 3,
            'grid_columns_mobile' => 2,
            'show_on_shop' => 'yes',
            'show_on_category' => 'yes',
            'show_on_single' => 'yes',
            'custom_icon_id' => 0,
        );
        
        foreach ($default_options as $option => $value) {
            if (get_option('wp_favorites_' . $option) === false) {
                add_option('wp_favorites_' . $option, $value);
            }
        }
    }
}

/**
 * Initialize the plugin
 */
function wp_favorites_init() {
    return WP_Favorites_Plugin::get_instance();
}

// Start the plugin
wp_favorites_init();
